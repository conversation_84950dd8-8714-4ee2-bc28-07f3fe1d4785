{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@genkit-ai/googleai": "^1.14.1", "@genkit-ai/next": "^1.14.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "framer-motion": "^11.3.19", "genkit": "^1.14.1", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "leaflet.markercluster": "^1.5.3", "lottie-react": "^2.4.0", "lucide-react": "^0.475.0", "next": "^15.5.4", "patch-package": "^8.0.0", "react": "^19.2.0", "react-day-picker": "^8.10.1", "react-dom": "^19.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/leaflet": "^1.9.12", "@types/leaflet.markercluster": "^1.5.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "file-loader": "^6.2.0", "genkit-cli": "^1.14.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "url-loader": "^4.1.1"}, "overrides": {"react-day-picker": {"react": "$react", "react-dom": "$react-dom"}}, "resolutions": {"react": "^19.2.0", "react-dom": "^19.2.0", "@types/react": "^19", "@types/react-dom": "^19"}}