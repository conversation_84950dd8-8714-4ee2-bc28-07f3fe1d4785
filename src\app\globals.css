@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 210 40% 98%;

    --card: 0 100% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 100% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 100% 25%;
    --secondary-foreground: 210 40% 98%;

    --muted: 0 100% 12%;
    --muted-foreground: 210 40% 60%;

    --accent: 0 100% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 100% 25%;
    --input: 0 100% 18%;
    --ring: 210 40% 98%;

    --radius: 0.5rem;

    --chart-1: 210 40% 98%;
    --chart-2: 210 40% 88%;
    --chart-3: 210 40% 78%;
    --chart-4: 210 40% 68%;
    --chart-5: 210 40% 58%;
  }

  .dark {
    --background: 0 0% 4%;
    --foreground: 210 40% 98%;

    --card: 0 100% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 100% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 100% 25%;
    --secondary-foreground: 210 40% 98%;

    --muted: 0 100% 12%;
    --muted-foreground: 210 40% 60%;

    --accent: 0 100% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 100% 25%;
    --input: 0 100% 18%;
    --ring: 210 40% 98%;

    --chart-1: 210 40% 98%;
    --chart-2: 210 40% 88%;
    --chart-3: 210 40% 78%;
    --chart-4: 210 40% 68%;
    --chart-5: 210 40% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.neon-glow {
  @apply drop-shadow-[0_0_8px_hsl(var(--primary))];
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px #3b82f6, 0 0 20px #3b82f6, 0 0 30px #3b82f6;
  }
  to {
    text-shadow: 0 0 20px #3b82f6, 0 0 30px #3b82f6, 0 0 40px #3b82f6;
  }
}

.chart-animate {
  animation: chartFadeIn 1s ease-out;
}

@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-point-animate {
  animation: dataPointPulse 2s ease-in-out infinite;
}

@keyframes dataPointPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* Chart specific animations */
.recharts-line-curve {
  animation: drawLine 2s ease-out;
}

@keyframes drawLine {
  from {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
  }
}

.recharts-area-area {
  animation: fillArea 1.5s ease-out;
}

@keyframes fillArea {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.3;
  }
}

.recharts-scatter-symbol {
  animation: scatterAppear 0.8s ease-out;
}

@keyframes scatterAppear {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
