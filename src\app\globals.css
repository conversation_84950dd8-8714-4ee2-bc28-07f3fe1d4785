@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 210 40% 98%;

    --card: 0 100% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 100% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 100% 25%;
    --secondary-foreground: 210 40% 98%;

    --muted: 0 100% 12%;
    --muted-foreground: 210 40% 60%;

    --accent: 0 100% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 100% 25%;
    --input: 0 100% 18%;
    --ring: 210 40% 98%;

    --radius: 0.5rem;

    --chart-1: 210 40% 98%;
    --chart-2: 210 40% 88%;
    --chart-3: 210 40% 78%;
    --chart-4: 210 40% 68%;
    --chart-5: 210 40% 58%;
  }

  .dark {
    --background: 0 0% 4%;
    --foreground: 210 40% 98%;

    --card: 0 100% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 100% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 100% 25%;
    --secondary-foreground: 210 40% 98%;

    --muted: 0 100% 12%;
    --muted-foreground: 210 40% 60%;

    --accent: 0 100% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 100% 25%;
    --input: 0 100% 18%;
    --ring: 210 40% 98%;

    --chart-1: 210 40% 98%;
    --chart-2: 210 40% 88%;
    --chart-3: 210 40% 78%;
    --chart-4: 210 40% 68%;
    --chart-5: 210 40% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.neon-glow {
  @apply drop-shadow-[0_0_8px_hsl(var(--primary))];
}
