# **App Name**: SharkTrack

## Core Features:

- Data Integration: Integrate NASA satellite data (SWOT, PACE) to gather oceanographic features and phytoplankton data.
- Habitat Prediction: Use machine learning to predict shark foraging habitats based on integrated data and environmental variables, creating global maps of potential shark activity.
- Conceptual Tag Model: Develop a conceptual model for a shark tag that measures dietary intake and transmits data in real-time, including suggesting specific sensors.
- Interactive Maps: Display predicted shark foraging hotspots on an interactive map interface using data from the NASA satellites.
- Variable Correlation Tool: Allow users to select and visualize the correlations between sea surface variables and shark activity, powered by an LLM tool that can suggest related but non-obvious parameters.
- Shark Facts: Display information about sharks to improve community knowledge, why predictions of shark locations matter, and the effects they can have on humans.
- Shark Tag Visualization: Visualize the tag placement on a shark and what information could be sent back via that device.

## Style Guidelines:

- Primary color: Dark blue (#001F3F) for a deep ocean feel.
- Secondary color: Black (#000000) for background and contrast.
- Accent color: Electric blue (#7DF9FF) to highlight interactive elements and data visualizations.
- Font pairing: 'Roboto' (sans-serif) for headlines and 'Open Sans' (sans-serif) for body text, ensuring readability on dark backgrounds.
- Use neon-style icons to represent data layers, tag functionalities, and shark species.
- Responsive design with a focus on high contrast for readability on dark backgrounds, emphasizing clear data presentation and map interactions.
- Subtle glow animations when loading data or displaying new predictions, providing visual feedback in a dark theme.