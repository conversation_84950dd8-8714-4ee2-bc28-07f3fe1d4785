# Sharks from Space - NASA ML Shark Prediction

A Next.js application that uses NASA satellite data to predict shark foraging locations through machine learning.

## Features

### Interactive Data Visualizations

- **Phytoplankton Distribution & Ocean Currents**: Animated area chart showing seasonal phytoplankton concentration patterns and ocean current speeds
- **Temperature vs Phytoplankton Growth**: Line graph displaying correlation between ocean temperature and phytoplankton growth rates
- **Chlorophyll vs Biomass**: Scatter plot showing satellite-measured chlorophyll concentration correlation with phytoplankton biomass

### Pages

- **Home** (`/`): Landing page with navigation
- **Learn** (`/info`): Information about the marine food chain and ML model
- **Map** (`/map`): Interactive map showing shark foraging hotspots
- **Model** (`/brain`): NASA ML model specifications and data visualizations
- **Hardware** (`/sharktag`): Shark tagging hardware information
- **Team** (`/members`): Team member information

## Tech Stack

- Next.js 15.5.4
- React 19.2.0
- Recharts 2.15.1 (for data visualizations)
- Tailwind CSS
- Framer Motion (for animations)
- Leaflet (for maps)

## Getting Started

To run locally:

```bash
npm install
npm run dev
```

The application will be available at `http://localhost:9002`

## Deployment

This application is designed to be deployed directly to GitHub Pages or similar static hosting services without requiring additional dependencies.
