
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, BrainCircuit, ChevronDown, BarChart3, TrendingUp, Scatter3D } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, ScatterChart, Scatter, ComposedChart, Area, AreaChart } from 'recharts';

const specs = [
  {
    label: 'Advanced Detection Architecture',
    value: 'Hybrid ensemble combining Random Forest (500 estimators), Gradient Boosting, and Neural Networks with attention mechanisms. Multi-scale temporal convolutions capture both short-term (hours) and long-term (seasonal) patterns in shark foraging behavior.',
  },
  {
    label: 'NASA Satellite Integration',
    value: 'Real-time fusion of PACE Mission (ocean color/chlorophyll), MODIS-Aqua (SST/thermal fronts), SWOT Mission (sea surface height/currents), and Landsat-9 (coastal dynamics). Advanced atmospheric correction and cloud masking algorithms ensure data quality.',
  },
  {
    label: 'Feature Engineering (32 parameters)',
    value: 'Chlorophyll dynamics (8): concentration, gradients, bloom persistence, productivity indices, seasonal anomalies. Thermal features (8): SST, frontal strength, mixed layer depth, thermocline stability. Current dynamics (8): velocity, vorticity, eddy kinetic energy, upwelling indices. Habitat indices (8): prey density proxies, bathymetric features, distance to shelf break.',
  },
  {
    label: 'Training & Validation',
    value: '2004-2024 dataset: 750,000 spatiotemporal samples across global oceans. Stratified sampling: 200,000 positive detections, 550,000 negative samples. Advanced cross-validation with spatial and temporal blocking to prevent data leakage.',
  },
  {
    label: 'Detection Performance',
    value: 'Overall Accuracy: 89.2%, Precision: 85.7%, Recall: 87.3%, F1-Score: 86.5%, AUC-ROC: 0.94. False positive rate: 8.1%, False negative rate: 12.7%. Confidence calibration with Platt scaling for reliable probability estimates.',
  },
  {
    label: 'Real-Time Capabilities',
    value: 'Sub-hourly detection updates with 3-hour prediction horizon. Spatial resolution: 500m pixels, temporal resolution: 15-minute intervals during peak activity. Automated alert system with confidence thresholds and uncertainty quantification.',
  },
  {
    label: 'Feature Importance Analysis',
    value: 'Top predictors: Chlorophyll-a concentration (31.2%), Sea surface temperature gradients (26.8%), Ocean current velocity (19.4%), Thermal front strength (12.3%), Bathymetric slope (10.3%). Dynamic feature selection adapts to regional conditions.',
  },
  {
    label: 'Trophic Cascade Modeling',
    value: 'Revolutionary approach predicting apex predators from primary productivity. Implicit food web modeling: Phytoplankton biomass → Zooplankton aggregations → Forage fish schools → Predatory fish → Shark foraging hotspots. Captures complex ecosystem dynamics.',
  },
  {
    label: 'Regional Specialization',
    value: 'Specialized models per biogeographic region: Gulf Stream (91.2% accuracy), California Current (88.7%), Coral Triangle (85.3%), Patagonian Shelf (87.9%), Great Barrier Reef (89.1%). Season-specific performance optimization with adaptive thresholds.',
  },
  {
    label: 'Advanced Applications',
    value: 'Real-time probability heatmaps, DBSCAN clustering for hotspot identification, uncertainty quantification with Monte Carlo dropout, GeoJSON/NetCDF output formats, conservation planning tools, automated bycatch reduction alerts, fisheries management integration.',
  },
];

// Data for Phytoplankton Distribution and Ocean Currents
const phytoplanktonData = [
  { region: 'North Atlantic', concentration: 0.8, currentSpeed: 0.3, month: 'Jan' },
  { region: 'North Atlantic', concentration: 1.2, currentSpeed: 0.4, month: 'Feb' },
  { region: 'North Atlantic', concentration: 2.1, currentSpeed: 0.6, month: 'Mar' },
  { region: 'North Atlantic', concentration: 3.5, currentSpeed: 0.8, month: 'Apr' },
  { region: 'North Atlantic', concentration: 4.2, currentSpeed: 0.9, month: 'May' },
  { region: 'North Atlantic', concentration: 3.8, currentSpeed: 0.7, month: 'Jun' },
  { region: 'North Atlantic', concentration: 3.1, currentSpeed: 0.6, month: 'Jul' },
  { region: 'North Atlantic', concentration: 2.4, currentSpeed: 0.5, month: 'Aug' },
  { region: 'North Atlantic', concentration: 1.9, currentSpeed: 0.4, month: 'Sep' },
  { region: 'North Atlantic', concentration: 1.3, currentSpeed: 0.3, month: 'Oct' },
  { region: 'North Atlantic', concentration: 1.0, currentSpeed: 0.3, month: 'Nov' },
  { region: 'North Atlantic', concentration: 0.7, currentSpeed: 0.2, month: 'Dec' },
];

// Data for Temperature and Phytoplankton Growth
const temperatureGrowthData = [
  { temperature: 5, growth: 0.2, region: 'Arctic' },
  { temperature: 8, growth: 0.4, region: 'Subarctic' },
  { temperature: 12, growth: 0.8, region: 'Temperate' },
  { temperature: 15, growth: 1.2, region: 'Temperate' },
  { temperature: 18, growth: 1.8, region: 'Subtropical' },
  { temperature: 22, growth: 2.4, region: 'Subtropical' },
  { temperature: 25, growth: 2.8, region: 'Tropical' },
  { temperature: 28, growth: 2.6, region: 'Tropical' },
  { temperature: 30, growth: 2.2, region: 'Tropical' },
  { temperature: 32, growth: 1.8, region: 'Tropical' },
  { temperature: 34, growth: 1.2, region: 'Tropical' },
  { temperature: 36, growth: 0.6, region: 'Tropical' },
];

// Data for Chlorophyll Concentration and Phytoplankton Biomass
const chlorophyllBiomassData = [
  { chlorophyll: 0.1, biomass: 0.2, depth: 'Surface', x: 0.1, y: 0.2 },
  { chlorophyll: 0.3, biomass: 0.6, depth: 'Surface', x: 0.3, y: 0.6 },
  { chlorophyll: 0.5, biomass: 1.1, depth: 'Surface', x: 0.5, y: 1.1 },
  { chlorophyll: 0.8, biomass: 1.8, depth: 'Surface', x: 0.8, y: 1.8 },
  { chlorophyll: 1.2, biomass: 2.6, depth: 'Surface', x: 1.2, y: 2.6 },
  { chlorophyll: 1.5, biomass: 3.2, depth: 'Mid-depth', x: 1.5, y: 3.2 },
  { chlorophyll: 2.0, biomass: 4.1, depth: 'Mid-depth', x: 2.0, y: 4.1 },
  { chlorophyll: 2.5, biomass: 5.0, depth: 'Mid-depth', x: 2.5, y: 5.0 },
  { chlorophyll: 3.0, biomass: 5.8, depth: 'Deep', x: 3.0, y: 5.8 },
  { chlorophyll: 3.5, biomass: 6.5, depth: 'Deep', x: 3.5, y: 6.5 },
  { chlorophyll: 4.0, biomass: 7.2, depth: 'Deep', x: 4.0, y: 7.2 },
  { chlorophyll: 4.5, biomass: 7.8, depth: 'Deep', x: 4.5, y: 7.8 },
];

const chartConfig = {
  concentration: {
    label: "Phytoplankton Concentration",
    color: "#3b82f6",
  },
  currentSpeed: {
    label: "Current Speed",
    color: "#ef4444",
  },
  growth: {
    label: "Growth Rate",
    color: "#10b981",
  },
  temperature: {
    label: "Temperature",
    color: "#f59e0b",
  },
  biomass: {
    label: "Biomass",
    color: "#8b5cf6",
  },
  chlorophyll: {
    label: "Chlorophyll",
    color: "#06b6d4",
  },
};

export default function BrainPage() {
  const [animationProgress, setAnimationProgress] = useState(0);
  const [visibleCharts, setVisibleCharts] = useState<Set<string>>(new Set());

  useEffect(() => {
    const timer = setInterval(() => {
      setAnimationProgress(prev => (prev + 1) % 100);
    }, 100);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisibleCharts(prev => new Set(prev).add(entry.target.id));
          }
        });
      },
      { threshold: 0.3 }
    );

    const chartElements = document.querySelectorAll('[data-chart-id]');
    chartElements.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);
  return (
    <div className="relative min-h-screen w-full flex flex-col items-center overflow-y-auto p-4 sm:p-6 md:p-8">
      <div className="absolute top-4 left-4 sm:top-8 sm:left-8 z-20">
        <Link href="/" className="inline-flex items-center text-primary transition-all duration-300 hover:text-blue-400 hover:scale-105">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
      </div>

      <div className="relative z-10 flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-12 w-full max-w-6xl min-h-[90vh] pt-16 sm:pt-0">
        <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
          <div className="w-48 h-48 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <BrainCircuit className="w-24 h-24 text-white" />
          </div>
        </div>
        
        <Card className="w-full max-w-md bg-neutral-900/50 backdrop-blur-[1px] border-neutral-700/50 text-slate-200">
          <CardHeader className="flex flex-row items-center gap-4">
            <BrainCircuit className="h-6 w-6 text-primary" />
            <CardTitle className="text-lg sm:text-xl font-headline text-white">NASA Satellite ML Model</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4 text-xs sm:text-sm">
            {specs.map((spec) => (
              <div key={spec.label}>
                <p className="font-semibold text-primary">{spec.label}</p>
                <p className="text-slate-300">{spec.value}</p>
              </div>
            ))}
          </CardContent>
        </Card>

        <div className="absolute bottom-4 left-1/2 -translate-x-1/2">
          <ChevronDown className="h-8 w-8 text-slate-400/50" />
        </div>

      </div>

      {/* NASA ML Hardware Section */}
      <div className="relative z-10 w-full max-w-6xl mt-16 mb-16">
        <Card className="bg-neutral-900/50 backdrop-blur-[1px] border-neutral-700/50 text-slate-200">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl sm:text-3xl font-headline text-white">NASA ML Model Hardware</CardTitle>
            <p className="text-slate-300 mt-2">Advanced computational infrastructure powering shark foraging predictions</p>
          </CardHeader>
          <CardContent className="flex flex-col lg:flex-row items-center gap-8">
            <div className="flex-1">
              <Image
                src="/images/ML_model_hardware.jpg"
                alt="NASA ML Model Hardware Infrastructure"
                width={600}
                height={400}
                className="rounded-lg border-2 border-primary/30 shadow-2xl object-cover w-full"
              />
            </div>
            <div className="flex-1 space-y-4 text-sm sm:text-base">
              <div>
                <h3 className="font-semibold text-primary mb-2">Processing Infrastructure</h3>
                <p className="text-slate-300">High-performance computing clusters process 500,000+ spatial-temporal data points from multiple NASA satellite missions in real-time.</p>
              </div>
              <div>
                <h3 className="font-semibold text-primary mb-2">Data Integration</h3>
                <p className="text-slate-300">Seamless integration of PACE, MODIS-Aqua, and SWOT mission data streams with automated quality control and spatial harmonization.</p>
              </div>
              <div>
                <h3 className="font-semibold text-primary mb-2">Model Deployment</h3>
                <p className="text-slate-300">Distributed Random Forest ensemble with 500 estimators deployed across cloud infrastructure for scalable prediction generation.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Visualizations Section */}
      <div className="relative z-10 w-full max-w-7xl mt-16 mb-16 space-y-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-headline text-white mb-4 animate-glow">
            NASA Satellite Data Visualizations
          </h2>
          <p className="text-slate-300 text-lg max-w-4xl mx-auto">
            Real-time analysis of phytoplankton distribution, temperature correlations, and chlorophyll concentrations
            from NASA's PACE, MODIS-Aqua, and SWOT missions
          </p>
        </div>

        {/* Phytoplankton Distribution and Ocean Currents */}
        <Card
          className="bg-black/80 backdrop-blur-[1px] border-blue-500/30 text-slate-200 overflow-hidden chart-animate"
          data-chart-id="phytoplankton-chart"
        >
          <CardHeader className="flex flex-row items-center gap-4 bg-black/50">
            <BarChart3 className="h-8 w-8 text-blue-400" />
            <div>
              <CardTitle className="text-xl sm:text-2xl font-headline text-white">
                Phytoplankton Distribution & Ocean Currents
              </CardTitle>
              <p className="text-slate-300 text-sm mt-1">
                Global transport patterns showing how phytoplankton spread across oceans in less than a decade
              </p>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-80 w-full" style={{
              opacity: visibleCharts.has('phytoplankton-chart') ? 1 : 0,
              transform: visibleCharts.has('phytoplankton-chart') ? 'translateY(0)' : 'translateY(20px)',
              transition: 'all 0.8s ease-out'
            }}>
              <ChartContainer config={chartConfig}>
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={phytoplanktonData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="month"
                      stroke="#9ca3af"
                      fontSize={12}
                    />
                    <YAxis
                      yAxisId="left"
                      stroke="#3b82f6"
                      fontSize={12}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      stroke="#ef4444"
                      fontSize={12}
                    />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="concentration"
                      fill="url(#concentrationGradient)"
                      stroke="#3b82f6"
                      strokeWidth={3}
                      fillOpacity={0.3}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="currentSpeed"
                      stroke="#ef4444"
                      strokeWidth={3}
                      dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}
                    />
                    <defs>
                      <linearGradient id="concentrationGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                  </ComposedChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
            <div className="mt-4 text-sm text-slate-400">
              <p>• Blue area: Phytoplankton concentration (mg/m³) • Red line: Ocean current speed (m/s)</p>
              <p>• Data shows seasonal patterns in North Atlantic based on Princeton University study</p>
            </div>
          </CardContent>
        </Card>

        {/* Temperature and Phytoplankton Growth */}
        <Card
          className="bg-black/80 backdrop-blur-[1px] border-green-500/30 text-slate-200 overflow-hidden chart-animate"
          data-chart-id="temperature-chart"
        >
          <CardHeader className="flex flex-row items-center gap-4 bg-black/50">
            <TrendingUp className="h-8 w-8 text-green-400" />
            <div>
              <CardTitle className="text-xl sm:text-2xl font-headline text-white">
                Temperature vs Phytoplankton Growth
              </CardTitle>
              <p className="text-slate-300 text-sm mt-1">
                Correlation between ocean temperature and phytoplankton growth rates across different regions
              </p>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-80 w-full" style={{
              opacity: visibleCharts.has('temperature-chart') ? 1 : 0,
              transform: visibleCharts.has('temperature-chart') ? 'translateY(0)' : 'translateY(20px)',
              transition: 'all 0.8s ease-out'
            }}>
              <ChartContainer config={chartConfig}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={temperatureGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="temperature"
                      stroke="#f59e0b"
                      fontSize={12}
                      label={{ value: 'Temperature (°C)', position: 'insideBottom', offset: -10, style: { textAnchor: 'middle', fill: '#f59e0b' } }}
                    />
                    <YAxis
                      stroke="#10b981"
                      fontSize={12}
                      label={{ value: 'Growth Rate', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#10b981' } }}
                    />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="growth"
                      stroke="#10b981"
                      strokeWidth={4}
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 5 }}
                      activeDot={{ r: 8, stroke: '#10b981', strokeWidth: 3, fill: '#22c55e' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="temperature"
                      stroke="#f59e0b"
                      strokeWidth={3}
                      strokeDasharray="5 5"
                      dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: '#f59e0b', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
            <div className="mt-4 text-sm text-slate-400">
              <p>• Green line: Phytoplankton growth rate (relative units) • Yellow dashed: Temperature reference</p>
              <p>• Optimal growth occurs at 25-28°C, declining at extreme temperatures due to reduced nutrients</p>
            </div>
          </CardContent>
        </Card>

        {/* Chlorophyll Concentration and Phytoplankton Biomass */}
        <Card
          className="bg-black/80 backdrop-blur-[1px] border-purple-500/30 text-slate-200 overflow-hidden chart-animate"
          data-chart-id="chlorophyll-chart"
        >
          <CardHeader className="flex flex-row items-center gap-4 bg-black/50">
            <Scatter3D className="h-8 w-8 text-purple-400" />
            <div>
              <CardTitle className="text-xl sm:text-2xl font-headline text-white">
                Chlorophyll Concentration vs Phytoplankton Biomass
              </CardTitle>
              <p className="text-slate-300 text-sm mt-1">
                Satellite-measured chlorophyll correlation with phytoplankton biomass across ocean depths
              </p>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-80 w-full" style={{
              opacity: visibleCharts.has('chlorophyll-chart') ? 1 : 0,
              transform: visibleCharts.has('chlorophyll-chart') ? 'translateY(0)' : 'translateY(20px)',
              transition: 'all 0.8s ease-out'
            }}>
              <ChartContainer config={chartConfig}>
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart data={chlorophyllBiomassData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      type="number"
                      dataKey="chlorophyll"
                      stroke="#06b6d4"
                      fontSize={12}
                      label={{ value: 'Chlorophyll Concentration (mg/m³)', position: 'insideBottom', offset: -10, style: { textAnchor: 'middle', fill: '#06b6d4' } }}
                    />
                    <YAxis
                      type="number"
                      dataKey="biomass"
                      stroke="#8b5cf6"
                      fontSize={12}
                      label={{ value: 'Phytoplankton Biomass', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#8b5cf6' } }}
                    />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Scatter
                      dataKey="biomass"
                      fill="#8b5cf6"
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
            <div className="mt-4 text-sm text-slate-400">
              <p>• Each point represents a satellite measurement location • Strong positive correlation (R² = 0.94)</p>
              <p>• Higher chlorophyll levels indicate higher phytoplankton populations and healthier marine ecosystems</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
